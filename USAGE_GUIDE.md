# Solana MEV Bot - 使用指南和最佳实践

## 快速开始

### 1. 环境准备

**系统要求：**
- Rust 1.70+ 
- Solana CLI 1.18+
- MongoDB (可选，用于数据存储)
- 至少 8GB RAM
- 稳定的网络连接

**安装依赖：**
```bash
# 安装 Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 安装 Solana CLI
sh -c "$(curl -sSfL https://release.solana.com/v1.18.9/install)"

# 克隆项目
git clone https://github.com/your-repo/solana-mev-bot-optimized.git
cd solana-mev-bot-optimized
```

### 2. 配置设置

**创建 `.env` 文件：**
```bash
# RPC 配置 - 使用高性能私有 RPC
RPC_URL=https://api.mainnet-beta.solana.com
RPC_URL_TX=https://your-private-rpc-endpoint.com
DEVNET_RPC_URL=https://api.devnet.solana.com
WSS_RPC_URL=wss://your-websocket-endpoint.com

# 钱包配置
PAYER_KEYPAIR_PATH=/path/to/your/keypair.json

# 数据库配置 (可选)
DATABASE_NAME=mev_bot_db

# 模拟器配置 (可选)
SIMULATOR_URL=http://localhost:3000
WS_SIMULATOR_URL=ws://localhost:3001

# Geyser 配置 (可选)
GEYSER_URL=grpc://your-geyser-endpoint.com
GEYSER_ACCESS_TOKEN=your-access-token
```

**生成钱包：**
```bash
# 生成新钱包
solana-keygen new --outfile ~/solana-wallet.json

# 或导入现有钱包
solana-keygen recover --outfile ~/solana-wallet.json
```

### 3. 编译和运行

**编译项目：**
```bash
cargo build --release
```

**测试运行：**
```bash
# 在 devnet 上测试
cargo run --release -- --test

# 主网运行
cargo run --release
```

## 配置详解

### 主要参数配置

**在 `main.rs` 中修改关键参数：**

```rust
// 模拟交易金额 (lamports)
let simulation_amount = 3500000000; // 3.5 SOL
// let simulation_amount = 1000000000; // 1 SOL (保守)
// let simulation_amount = 5000000000; // 5 SOL (激进)

// 策略开关
let massive_strategie: bool = true;   // 大规模扫描策略
let best_strategie: bool = true;      // 最佳路径策略  
let optimism_strategie: bool = true;  // 优化执行策略

// 高级配置
let fetch_new_pools = false;          // 是否获取新池 (谨慎使用)
let restrict_sol_usdc = true;         // 限制 SOL/USDC 池数量
```

### 代币配置

**添加新的套利代币对：**
```rust
InputVec {
    tokens_to_arb: vec![
        TokenInArb {
            address: String::from("So11111111111111111111111111111111111111112"), 
            symbol: String::from("SOL")
        }, // 基础代币 (必须是 SOL)
        TokenInArb {
            address: String::from("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), 
            symbol: String::from("USDC")
        },
        // 添加更多代币...
    ],
    include_1hop: true,               // 启用 1-hop 套利
    include_2hop: true,               // 启用 2-hop 套利
    numbers_of_best_paths: 4,         // 保留最佳路径数量
    get_fresh_pools_bool: false       // 获取新池 (影响性能)
}
```

### 风险参数调整

**盈利阈值设置：**
```rust
// 在 strategies.rs 中修改
if result_difference > 20000000.0 { // 0.02 SOL 最小盈利
    // 执行交易
}

// 建议设置：
// 保守: 50000000.0  (0.05 SOL)
// 平衡: 20000000.0  (0.02 SOL) 
// 激进: 10000000.0  (0.01 SOL)
```

**流动性过滤：**
```rust
// 在 calc_arb.rs 中修改最小流动性要求
DexLabel::RAYDIUM => {
    if market.liquidity.unwrap() >= 5000 { // 提高到 $5000
        sorted_markets_arb.insert(key, market);
    }
}
```

## 运行模式

### 1. 测试模式

**Devnet 测试：**
```bash
# 修改 main.rs 中的链类型
ChainType::Devnet

# 运行测试
cargo run --release
```

**模拟模式：**
```rust
// 在 create_transaction.rs 中设置
SendOrSimulate::Simulate  // 仅模拟，不发送交易
```

### 2. 生产模式

**主网运行：**
```rust
// 确保配置正确
ChainType::Mainnet
SendOrSimulate::Send
```

**监控运行：**
```bash
# 使用 screen 或 tmux 后台运行
screen -S mev-bot
cargo run --release
# Ctrl+A, D 分离会话

# 重新连接
screen -r mev-bot
```

### 3. 策略模式

**大规模扫描模式：**
- 适用于发现新机会
- 资源消耗较大
- 建议在性能较好的服务器上运行

**最佳路径模式：**
- 基于预计算的最佳路径
- 资源消耗较小
- 适合持续监控

**优化执行模式：**
- 执行预先识别的高价值交易
- 最低延迟
- 适合高频交易

## 性能优化

### 1. 硬件优化

**推荐配置：**
- CPU: 8核心以上
- RAM: 16GB 以上
- 存储: SSD
- 网络: 低延迟连接

**系统优化：**
```bash
# 增加文件描述符限制
ulimit -n 65536

# 优化网络参数
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
sysctl -p
```

### 2. 网络优化

**RPC 选择：**
- 使用专用 RPC 节点
- 选择地理位置接近的节点
- 配置多个备用节点

**连接优化：**
```rust
// 在 create_transaction.rs 中调整
let iteration_number = 5;  // 重试次数
let compute_unit_limit = 1_400_000;  // 计算单元限制
let priority_fee = 100;  // 优先费用 (micro-lamports)
```

### 3. 算法优化

**路径过滤：**
```rust
// 减少计算量的设置
let include_1hop = true;   // 保持启用
let include_2hop = false;  // 如果性能不足可禁用
let numbers_of_best_paths = 2;  // 减少保留路径数量
```

**缓存优化：**
- 启用路径模拟缓存
- 定期清理过期缓存
- 监控内存使用情况

## 监控和调试

### 1. 日志配置

**日志级别设置：**
```yaml
# logging_config.yaml
appenders:
  stdout:
    kind: console
    encoder:
      pattern: "{d} [{l}] {m}{n}"
  
root:
  level: info  # debug, info, warn, error
  appenders:
    - stdout
```

**关键日志监控：**
```bash
# 监控盈利机会
grep "💸💸💸" logs/mev-bot.log

# 监控错误
grep "ERROR" logs/mev-bot.log

# 监控交易执行
grep "✅" logs/mev-bot.log
```

### 2. 性能监控

**系统资源监控：**
```bash
# CPU 和内存使用
top -p $(pgrep mev-bot)

# 网络连接
netstat -an | grep :8899  # RPC 连接

# 磁盘使用
df -h
du -sh results/ best_paths_selected/ optimism_transactions/
```

**应用指标监控：**
- 成功路径数量
- 失败路径比例
- 平均模拟时间
- 交易成功率

### 3. 故障排除

**常见问题：**

1. **RPC 连接失败**
   ```bash
   # 检查网络连接
   curl -X POST -H "Content-Type: application/json" \
        -d '{"jsonrpc":"2.0","id":1,"method":"getHealth"}' \
        $RPC_URL
   ```

2. **内存不足**
   ```bash
   # 监控内存使用
   free -h
   # 重启程序释放内存
   ```

3. **交易失败**
   ```rust
   // 检查滑点设置
   let estimated_min_amount_out = (estimated_amount_out * 0.95).to_string();
   ```

## 最佳实践

### 1. 安全实践

**资金管理：**
- 使用专用钱包进行套利
- 设置合理的最大交易金额
- 定期提取利润到冷钱包

**私钥安全：**
- 使用硬件钱包生成私钥
- 加密存储私钥文件
- 限制服务器访问权限

### 2. 运营实践

**渐进式部署：**
1. Devnet 测试
2. 小额主网测试
3. 逐步增加交易金额
4. 全量生产运行

**监控告警：**
- 设置盈利阈值告警
- 监控异常错误率
- 跟踪资金变化

**定期维护：**
- 更新代币列表
- 清理历史数据
- 优化参数配置

### 3. 合规实践

**法律合规：**
- 了解当地法律法规
- 记录所有交易活动
- 准备税务申报材料

**技术合规：**
- 遵循 DEX 使用条款
- 避免恶意操作
- 维护系统稳定性

## 故障恢复

### 1. 数据恢复

**备份策略：**
```bash
# 定期备份重要文件
tar -czf backup-$(date +%Y%m%d).tar.gz \
    best_paths_selected/ \
    optimism_transactions/ \
    .env \
    src/
```

**恢复流程：**
1. 停止运行中的程序
2. 恢复配置文件
3. 重新编译程序
4. 验证配置正确性
5. 重启程序

### 2. 紧急处理

**程序崩溃：**
```bash
# 检查崩溃原因
journalctl -u mev-bot --since "1 hour ago"

# 重启程序
systemctl restart mev-bot
```

**资金异常：**
1. 立即停止程序
2. 检查钱包余额
3. 分析交易历史
4. 联系技术支持

## 扩展开发

### 1. 添加新 DEX

**实现步骤：**
1. 在 `markets/` 下创建新模块
2. 实现 DEX 特定的数据获取和模拟
3. 在 `DexLabel` 枚举中添加新标签
4. 更新交易构建逻辑

### 2. 添加新策略

**开发流程：**
1. 在 `arbitrage/strategies.rs` 中实现新策略
2. 添加配置参数
3. 集成到主程序流程
4. 测试和优化

### 3. 性能优化

**优化方向：**
- 算法复杂度优化
- 内存使用优化
- 网络请求优化
- 并发处理优化
